import asyncio
import uuid
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse
import logging
from mobile_agent.exception.sse import SSEException
from mobile_agent.agent.graph.sse_output import (
    format_sse,
    stream_messages,
)
from mobile_agent.agent.infra.message_web import SummaryMessageData
from mobile_agent.agent.mobile_use_agent import MobileUseAgent
from mobile_agent.middleware.middleware import APIException
from mobile_agent.service.session.manager import session_manager
import langgraph.errors
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter(
    prefix="/mobile-use/api/v1/agent",
    tags=["agent"],
)

@router.post("/upload/video", response_model=UploadVideoResponse)
async def api_upload_video(request: Request = None):
    """
    API for uploading video to cloud storage

    1. Direct file upload: multipart/form-data with video file

    Args:
        request: HTTP request (for sandbox file fetch mode)
        video: Uploaded video file (for direct upload mode)

    Returns:
        JSON response with cloud storage video URL
    """

    try:
        if not request:
            raise HTTPException(
                status_code=400,
                detail="Request body required for sandbox file fetch mode"
            )
        account_id = request.headers.get("X-Top-Account-Id")
        req = await request.json()
        sandbox_id = req.get("sandbox_id")
        video_file_path = req.get("video_file_path")

        # 验证必要参数
        if not sandbox_id:
            raise HTTPException(
                status_code=400,
                detail="Missing parameter sandbox_id"
            )
        if not video_file_path:
            raise HTTPException(
                status_code=400,
                detail="Missing parameter video_file_path"
            )

        logger.info(f"从沙箱 {sandbox_id} 获取视频文件: {video_file_path}")

        # 获取沙箱工具服务器端点
        try:
            sandbox_endpoint = ecs_manager.get_tool_server_endpoint(
                account_id, sandbox_id)
            if not sandbox_endpoint:
                raise HTTPException(
                    status_code=404,
                    detail=f"Sandbox {sandbox_id} not found or not accessible"
                )
        except Exception as e:
            logger.error(f"Failed to get sandbox endpoint: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get sandbox endpoint: {str(e)}"
            )

        # 直接通过tool_client获取文件，避免FileTool的asyncio.run()问题
        tool_client = await get_tool_async_client(sandbox_endpoint)

        try:
            # 读取文件内容 (binary mode) - 直接调用client而不使用asyncio.run
            file_tool = FileTool(client=tool_client)
            result = await file_tool.read_file(video_file_path)
            file_result = result.model_dump(exclude_unset=True)

            if not file_result.get("Result"):
                raise HTTPException(
                    status_code=500,
                    detail="Failed to fetch file from sandbox"
                )

            # 获取文件内容 (binary content) - 修复字段名并处理bytes类型
            file_content = file_result["Result"].get("content")
            logger.info(
                f"文件内容获取结果: 类型={type(file_content)}, 长度={len(file_content) if file_content else 0}")
            if not file_content:
                logger.error(
                    f"文件内容为空，Result结构: {list(file_result.get('Result', {}).keys())}")
                raise HTTPException(
                    status_code=500,
                    detail="Empty file content from sandbox"
                )

            # 文件内容已经是bytes类型，直接使用
            video_content = file_content

            if len(video_content) == 0:
                raise HTTPException(
                    status_code=400,
                    detail="Empty video file"
                )

            logger.info(f"从沙箱获取到视频文件，大小: {len(video_content)} bytes")

        except Exception as e:
            logger.error(f"从沙箱获取文件失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to fetch file from sandbox: {str(e)}"
            )

        try:
            storage_tool = StorageTool()
            storage_config = get_storage_config()

            # 生成唯一的文件名
            filename = f"video_recordings/{account_id}/{uuid.uuid4()}.mp4"
            bucket_name = storage_config.tos_bucket_name

            # 上传文件
            result = await storage_tool.put_object(
                bucket=bucket_name,
                key=filename,
                content=video_content
            )

            if not result.get("success"):
                error_detail = result.get("error", "上传失败")
                logger.error(f"云存储上传失败: {error_detail}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Video upload failed: {error_detail}"
                )

            # 构建视频URL
            res = await storage_tool.pre_signed_download_url(
                bucket=bucket_name, key=filename)
            video_url = res.get("signed_url")

            logger.info(f"视频上传成功: {video_url}, 大小: {len(video_content)} bytes")
            return {
                "success": True,
                "video_url": video_url,
                "video_size": len(video_content)
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"存储上传异常: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Video upload failed: {str(e)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        # 避免在日志中输出包含二进制数据的异常信息
        logger.error(f"Video upload API failed: {type(e).__name__}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@app.post("/analyze/video")
async def api_analyze_video(request: Request):
    """
    API for analyzing video content from TOS URL and generating prompts

    Args:
        request: HTTP request containing video_url

    Returns:
        JSON response with generated prompt
    """
    try:
        # 解析请求体
        req = await request.json()
        video_url = req.get("video_url")

        if not video_url:
            raise HTTPException(
                status_code=400,
                detail="Missing parameter video_url"
            )

        logger.info(f"开始分析TOS视频: {video_url}")

        model_name = "doubao-1-5-thinking-vision-pro-250428"
        ai_client = AsyncOpenAI(api_key=get_models().get(model_name).api_key,
                                base_url=get_models().get(model_name).base_url)
        model_client = AsyncChatModelClient(
            ai_client=ai_client, model_name=model_name)
        planner = Planner(model_client, None, "", "")
        analysis_result = await planner.generate_prompt(video_url)

        if analysis_result["success"]:
            return {
                "success": True,
                "prompt": analysis_result["prompt"],
                "video_url": video_url
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"Video analysis failed: {analysis_result.get('error', 'Unknown error')}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Video analysis API failed: {type(e).__name__}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


async def generate_prompt(self, video_url: str) -> Dict[str, Any]:
        """
        从TOS视频URL生成mobile Use Agent的操作指导prompt

        Args:
            video_url: TOS上的视频文件URL

        Returns:
            包含生成结果的字典
        """
        try:
            self.logger.info(f"开始分析TOS视频: {video_url}")

            lang = get_lang_config().lang
            video_analyze_prompt = get_video_analyze_agent_prompt(lang)
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "video_url",
                            "video_url": {
                                "url": video_url,
                                "fps": 2  # 每秒截取2帧画面，用于视频理解
                            }
                        },
                        {"type": "text", "text": video_analyze_prompt},
                    ]
                }
            ]

            self.logger.info("调用模型进行视频分析")
            response = await self.model_client.ai_client.chat.completions.create(
                model=self.model_client.model_name,
                messages=messages,
                max_tokens=4096,
                temperature=0.1,
                extra_body={
                    "thinking": {
                        "type": "disabled"
                    }
                }
            )

            generated_prompt = response.choices[0].message.content
            self.logger.info("视频分析完成, generated prompt: %s", generated_prompt)

            return {
                "success": True,
                "prompt": generated_prompt,
                "video_url": video_url
            }

        except Exception as e:
            self.logger.error(f"视频分析失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }